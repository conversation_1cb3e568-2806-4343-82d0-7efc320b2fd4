# Astro Starter Kit: Basics

```sh
pnpm create astro@latest -- --template basics
```

[![Open in StackBlitz](https://developer.stackblitz.com/img/open_in_stackblitz.svg)](https://stackblitz.com/github/withastro/astro/tree/latest/examples/basics)
[![Open with CodeSandbox](https://assets.codesandbox.io/github/button-edit-lime.svg)](https://codesandbox.io/p/sandbox/github/withastro/astro/tree/latest/examples/basics)
[![Open in GitHub Codespaces](https://github.com/codespaces/badge.svg)](https://codespaces.new/withastro/astro?devcontainer_path=.devcontainer/basics/devcontainer.json)

> 🧑‍🚀 **Seasoned astronaut?** Delete this file. Have fun!

![just-the-basics](https://github.com/withastro/astro/assets/2244813/a0a5533c-a856-4198-8470-2d67b1d7c554)

## 🚀 Project Structure

Inside of your Astro project, you'll see the following folders and files:

```text
/
├── public/
│   └── favicon.svg
├── src/
│   ├── layouts/
│   │   └── Layout.astro
│   └── pages/
│       └── index.astro
└── package.json
```

To learn more about the folder structure of an Astro project, refer to [our guide on project structure](https://docs.astro.build/en/basics/project-structure/).

## 🧞 Commands

All commands are run from the root of the project, from a terminal:

| Command                   | Action                                           |
| :------------------------ | :----------------------------------------------- |
| `pnpm install`             | Installs dependencies                            |
| `pnpm dev`             | Starts local dev server at `localhost:4321`      |
| `pnpm build`           | Build your production site to `./dist/`          |
| `pnpm preview`         | Preview your build locally, before deploying     |
| `pnpm astro ...`       | Run CLI commands like `astro add`, `astro check` |
| `pnpm astro -- --help` | Get help using the Astro CLI                     |

## 👀 Want to learn more?

Feel free to check [our documentation](https://docs.astro.build) or jump into our [Discord server](https://astro.build/chat).

# Multi-Business Website Testing

This repository contains automated testing scripts for a multi-business website that includes Farming, Real Estate, and Legal Services pages.

## Testing Setup

The testing suite includes:
- Lighthouse audits for performance, accessibility, and SEO
- Broken link checking
- Responsive design verification
- EmailJS integration testing

## Prerequisites

- Node.js >= 18.0.0
- npm or yarn

## Installation

1. Install dependencies:
```bash
npm install
```

## Running Tests

### Local Development Testing
```bash
npm test
```
This will run tests against `http://localhost:3000`

### Production Testing
```bash
npm run test:prod
```
This will run tests against your production URL

## Test Results

Test results are stored in the `test-results` directory:
- Individual page results are saved as JSON files
- A summary report (`test-summary.json`) is generated with average scores

## Tested Pages

The following pages are tested:
- Homepage (/)
- About page (/about)
- Contact page (/contact)
- Blog page (/blog)
- Farming pages:
  - /farming
  - /farming/equipment
  - /farming/services
- Real Estate pages:
  - /real-estate
  - /real-estate/properties
  - /real-estate/agents
- Legal Services pages:
  - /legal
  - /legal/services
  - /legal/team

## Customizing Tests

To modify the pages being tested or add new test cases, edit the `scripts/test-website.js` file.

## Contributing

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a new Pull Request
