---
import Layout from "../layouts/Layout.astro";
import BusinessHeroCarousel from "../components/businesses/BusinessHeroCarousel.tsx";
import FeatureBanners from "../components/businesses/FeatureBanners.astro";
import NewsSection from "../components/businesses/NewsSection.astro";
import ServicesSection from "../components/businesses/ServicesSection.astro";
import Footer from "../components/Footer.astro";
---
<Layout title="Corporate Solutions Group | Enterprise Services">
    <main>
        <BusinessHeroCarousel
            title="Corporate Solutions Group"
            subtitle="Strategic business services across diverse sectors for enterprise growth and development."
            theme={{
                primary: "#0E2A47",
                secondary: "#1E5288",
                accent: "#0077B6",
            }}
            images={[
                "/images/image_2.jpeg",
                "/images/image_29.jpeg",
            ]}
        />

        <FeatureBanners />
        <NewsSection />
        <ServicesSection />

        <!-- Social Media Section -->
        <section class="py-12 bg-white">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl font-bold text-center mb-10 text-gray-800">
                    Connect With Us
                </h2>
                <div class="flex justify-center space-x-6">
                    <a
                        href="https://www.linkedin.com/company/corporate-solutions-group"
                        target="_blank"
                        rel="noopener noreferrer"
                        class="text-blue-600 hover:text-blue-800 transition-colors"
                        aria-label="LinkedIn"
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-8 w-8"
                            fill="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                d="M22.5 0H1.5C.679 0 0 1.679 0 3.5v17C0 22.321.679 24 1.5 24h21c.821 0 1.5-.679 1.5-1.5v-17C24 1.679 23.321 0 22.5 0zm-11.5 20.5h-2v-6.5c0-1.387.514-2.583 1.5-3.465V9c0-1.103.897-2 2-2h.5v.5h-2v6h2c.935.897 1.5 2.093 1.5 3.5v2.5z"
                            ></path>
                        </svg>
                    </a>
                    <a
                        href="https://www.twitter.com/corporatesolutions"
                        target="_blank"
                        rel="noopener noreferrer"
                        class="text-blue-400 hover:text-blue-600 transition-colors"
                        aria-label="Twitter"
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-8 w-8"
                            fill="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                d="M24 4.557a9.83 9.83 0 01-2.828.775 4.938 4.938 0 002.165-2.724 9.862 9.862 0 01-3.127 1.195 4.92 4.92 0 00-8.395 4.488A13.936 13.936 0 011.671 3.149a4.91 4.91 0 001.523 6.566A4.897 4.897 0 01.96 9.659v.061a4.927 4.927 0 003.94 4.827A4.891 4.891 0 01.794 21.035 4.907 4.907 0 008.11 18.71a8.265 8.265 0 01-4.268.193v.061A9.899 9.899 0 010 16.162c0-5.514 4.486-10 10-10 3.559 0 6.517 1.989 7.665 4.557z"
                            ></path>
                        </svg>
                    </a>
                    <a
                        href="https://www.facebook.com/corporatesolutionsgroup"
                        target="_blank"
                        rel="noopener noreferrer"
                        class="text-blue-600 hover:text-blue-800 transition-colors"
                        aria-label="Facebook"
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-8 w-8"
                            fill="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                d="M22.676 0H1.324C.593 0 0 1.593 0 3.324v17.352C0 22.407.593 24 1.324 24h11.352v-9.294H9.829V9.602h2.711V6.672h-2.711V4.73c0-2.903 1.91-3.324 3.829-3.324 1.09 0 2.171.07 1.829.108v2.255h-1.27c-1.243 0-1.635.863-1.635 1.636v1.536h3.27l-.469 3.769h-2.801V24h4.615c2.361 0 3.829-1.92 3.829-4.418V3.324C24 1.593 23.407 0 22.676 0z"
                            ></path>
                        </svg>
                    </a>
                </div>
            </div>
        </section>
    </main>

    <Footer />
</Layout>
