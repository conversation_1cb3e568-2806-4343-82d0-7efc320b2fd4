---
import BusinessLayout from "../../../layouts/BusinessLayout.astro";
import BusinessHero from "../../../components/businesses/BusinessHero";
import ServiceCard from "../../../components/businesses/ServiceCard.astro";
import TeamMember from "../../../components/businesses/TeamMember.astro";
import ContactForm from "../../../components/businesses/ContactForm";
import TestimonialSlider from "../../../components/businesses/TestimonialSlider";

interface Theme {
    primary: string;
    secondary: string;
    accent: string;
}

interface Service {
    title: string;
    description: string;
    icon: string;
}

interface Agent {
    name: string;
    role: string;
    specialization: string;
    image: string;
}

interface Testimonial {
    name: string;
    role: string;
    content: string;
}

interface EmailJSConfig {
    serviceId: string;
    templateId: string;
    publicKey: string;
}

const theme: Theme = {
    primary: "#1E3A8A", // Deep blue
    secondary: "#475569", // Slate gray
    accent: "#3B82F6", // Bright blue
};

const services: Service[] = [
    {
        title: "Property Listings",
        description:
            "Browse our extensive collection of residential and commercial properties",
        icon: "home",
    },
    {
        title: "Market Analysis",
        description:
            "Expert insights into current real estate market trends and opportunities",
        icon: "chart-line",
    },
    {
        title: "Property Management",
        description:
            "Comprehensive property management services for investors and owners",
        icon: "building",
    },
];

const agents: Agent[] = [
    {
        name: "Sarah Johnson",
        role: "Senior Agent",
        specialization: "Residential Properties",
        image: "/images/agent-1.jpg",
    },
    {
        name: "Michael Chen",
        role: "Commercial Specialist",
        specialization: "Commercial Real Estate",
        image: "/images/agent-2.jpg",
    },
    {
        name: "Emily Rodriguez",
        role: "Investment Advisor",
        specialization: "Property Investment",
        image: "/images/agent-3.jpg",
    },
];

const testimonials: Testimonial[] = [
    {
        name: "David Wilson",
        role: "Homeowner",
        content:
            "Found my dream home through their exceptional service and market knowledge.",
    },
    {
        name: "Emily Davis",
        role: "Property Investor",
        content:
            "Their market analysis helped me make informed investment decisions.",
    },
    {
        name: "Robert Taylor",
        role: "Business Owner",
        content:
            "Professional and efficient in handling our commercial property needs.",
    },
];

// EmailJS configuration
const emailjsConfig: EmailJSConfig = {
    serviceId: import.meta.env.PUBLIC_EMAILJS_SERVICE_ID || "",
    templateId: import.meta.env.PUBLIC_EMAILJS_TEMPLATE_ID || "",
    publicKey: import.meta.env.PUBLIC_EMAILJS_PUBLIC_KEY || "",
};
---

<BusinessLayout
    title="Real Estate Services | Expert Property Solutions"
    description="Professional real estate services offering expertise in property listings, market analysis, and property management."
    theme={theme}
>
    <BusinessHero
        title="Expert Real Estate Solutions"
        subtitle="Find your dream property with our expert guidance"
        backgroundImage="/images/real-estate-hero.jpg"
        theme={theme}
    />

    <section class="business-section">
        <div class="business-container">
            <h2 class="business-heading">Our Services</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {
                    services.map((service: Service) => (
                        <ServiceCard
                            title={service.title}
                            description={service.description}
                            icon={service.icon}
                        />
                    ))
                }
            </div>
        </div>
    </section>

    <section class="business-section bg-gray-50">
        <div class="business-container">
            <h2 class="business-heading">Our Agents</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {
                    agents.map((agent: Agent) => (
                        <TeamMember
                            name={agent.name}
                            role={agent.role}
                            specialization={agent.specialization}
                            image={agent.image}
                        />
                    ))
                }
            </div>
        </div>
    </section>

    <section class="business-section">
        <div class="business-container">
            <h2 class="business-heading">Client Testimonials</h2>
            <TestimonialSlider testimonials={testimonials} />
        </div>
    </section>

    <section class="business-section bg-gray-50">
        <div class="business-container">
            <h2 class="business-heading">Contact Us</h2>
            <div class="max-w-2xl mx-auto">
                <ContactForm serviceType="real-estate" theme={theme} />
            </div>
        </div>
    </section>
</BusinessLayout>
