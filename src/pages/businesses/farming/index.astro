---
import BusinessLayout from "../../../layouts/BusinessLayout.astro";
import BusinessHero from "../../../components/businesses/BusinessHero";
import ServiceCard from "../../../components/businesses/ServiceCard.astro";
import TeamMember from "../../../components/businesses/TeamMember.astro";
import ContactForm from "../../../components/businesses/ContactForm";
import TestimonialSlider from "../../../components/businesses/TestimonialSlider";

const theme = {
    primary: "#2F4F4F", // Dark slate gray
    secondary: "#556B2F", // Dark olive green
    accent: "#8FBC8F", // Dark sea green
};

const services = [
    {
        title: "Crop Management",
        description:
            "Professional crop management services ensuring optimal yield and quality",
        icon: "seedling",
    },
    {
        title: "Seasonal Produce",
        description:
            "Fresh, seasonal produce grown using sustainable farming practices",
        icon: "leaf",
    },
    {
        title: "Farm Locations",
        description:
            "Multiple farm locations strategically placed for optimal growing conditions",
        icon: "map-marker",
    },
];

const teamMembers = [
    {
        name: "<PERSON>",
        role: "Farm Manager",
        specialization: "Crop Management",
        image: "/images/farmer-1.jpg",
    },
    {
        name: "<PERSON>",
        role: "Agricultural Expert",
        specialization: "Sustainable Farming",
        image: "/images/farmer-2.jpg",
    },
    {
        name: "Mike Brown",
        role: "Field Supervisor",
        specialization: "Farm Operations",
        image: "/images/farmer-3.jpg",
    },
];

const testimonials = [
    {
        name: "David Wilson",
        role: "Restaurant Owner",
        content:
            "The quality of their produce is exceptional. We've been working with them for years.",
    },
    {
        name: "Emily Davis",
        role: "Local Market",
        content:
            "Their seasonal produce is always fresh and of the highest quality.",
    },
    {
        name: "Robert Taylor",
        role: "Food Distributor",
        content:
            "Professional, reliable, and their farming practices are truly sustainable.",
    },
];
---

<BusinessLayout
    title="Farming Services | Expert Agricultural Solutions"
    description="Professional farming services offering expertise in crop management, seasonal produce, and sustainable farming practices."
    theme={theme}
>
    <BusinessHero
        title="Expert Farming Solutions"
        subtitle="Sustainable farming practices for a better tomorrow"
        backgroundImage="/images/farming-hero.jpg"
        theme={theme}
    />

    <section class="business-section">
        <div class="business-container">
            <h2 class="business-heading">Our Services</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {
                    services.map((service) => (
                        <ServiceCard
                            title={service.title}
                            description={service.description}
                            icon={service.icon}
                        />
                    ))
                }
            </div>
        </div>
    </section>

    <section class="business-section bg-gray-50">
        <div class="business-container">
            <h2 class="business-heading">Our Team</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {
                    teamMembers.map((member) => (
                        <TeamMember
                            name={member.name}
                            role={member.role}
                            specialization={member.specialization}
                            image={member.image}
                        />
                    ))
                }
            </div>
        </div>
    </section>

    <section class="business-section">
        <div class="business-container">
            <h2 class="business-heading">Client Testimonials</h2>
            <TestimonialSlider testimonials={testimonials} />
        </div>
    </section>

    <section class="business-section bg-gray-50">
        <div class="business-container">
            <h2 class="business-heading">Contact Us</h2>
            <div class="max-w-2xl mx-auto">
                <ContactForm serviceType="farming" theme={theme} />
            </div>
        </div>
    </section>
</BusinessLayout>
