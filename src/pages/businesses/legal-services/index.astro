---
import BusinessLayout from "../../../layouts/BusinessLayout.astro";
import BusinessHeroAstro from "../../../components/businesses/BusinessHeroAstro.astro";
import ServiceCard from "../../../components/businesses/ServiceCard.astro";
import ContactForm from "../../../components/businesses/ContactForm";

const theme = {
  primary: "#1a1a1a",
  secondary: "#4a4a4a",
  accent: "#808080",
};

const auditServices = [
  {
    title: "Assurance Services",
    description: "Comprehensive audits to ensure financial accuracy and regulatory adherence",
    icon: "shield-check"
  },
  {
    title: "Non-Assurance Services",
    description: "Reviews and advisory to support business objectives",
    icon: "clipboard-list"
  },
  {
    title: "Quality Control",
    description: "Highest standards validated by ACCA reviews for ICAG",
    icon: "star"
  },
  {
    title: "Industry Expertise",
    description: "Serving manufacturing, trading, construction, medical, schools, extractive industries, and airlines",
    icon: "building"
  }
];

const accountingServices = [
  {
    title: "System Design & Implementation",
    description: "Custom accounting systems to streamline financial processes",
    icon: "settings"
  },
  {
    title: "Data Entry & Management",
    description: "Accurate recording of financial transactions",
    icon: "database"
  },
  {
    title: "Budgeting & MIS Reports",
    description: "Tools for effective financial planning and decision-making",
    icon: "trending-up"
  },
  {
    title: "IFRS Financial Reporting",
    description: "Compliant and transparent financial statements",
    icon: "file-text"
  },
  {
    title: "Financial Analysis",
    description: "Detailed insights including ageing reports",
    icon: "bar-chart"
  },
  {
    title: "Asset Accounting",
    description: "Fixed Assets Register, depreciation, and amortization",
    icon: "package"
  }
];

const whyChooseUs = [
  {
    title: "Experienced Leadership",
    description: "25+ years expertise from Vishwamint Ramnarine, Chartered Accountant",
    icon: "award"
  },
  {
    title: "Professional Team",
    description: "Energetic, trained professionals ensuring prompt service",
    icon: "users"
  },
  {
    title: "Technology-Driven",
    description: "Secure networked systems with backups and zero downtime",
    icon: "cpu"
  },
  {
    title: "Client-Centric Approach",
    description: "Long-term relationships through personalized solutions",
    icon: "heart"
  }
];

const leadershipProfiles = [
  {
    name: "Vishwamint Ramnarine",
    role: "Managing Partner",
    background: "25 years at PKF, from Junior Audit Clerk to Senior Partner",
    qualifications: "ACCA Fellow (2008), MSc Professional Accountancy (2022), ICAG practice license, Guyana Revenue Authority tax certificate",
    roles: "ICAG President, ICAC Director, former PKF Caribbean Chairman (2020-2023), Guyana Legal Aid Clinic Director",
    expertise: "High-quality audits, continuous professional development"
  },
  {
    name: "Sandia Rebekah Harold-Ramnarine",
    role: "Associate",
    background: "10 years as ACCA graduate and Attorney at Law",
    experience: "Guyana Revenue Authority (Tax Trainee, VAT Auditor, Corporate Secretary), Guyana Power and Light (Billing Manager), Cellsmart Inc. (Internal Auditor)",
    focus: "Corporate services, business development, conveyances, probate, family law",
    leadership: "Founder of WIST Ministries International Inc., a 21-year not-for-profit"
  }
];

const additionalServices = [
  {
    title: "Taxation & Litigation Support",
    description: "Tax planning, compliance, and support for corporate and individual clients",
    icon: "file-tax"
  },
  {
    title: "Management Consultancy",
    description: "Payroll, creditor payments, debt collection, and business optimization services",
    icon: "chart-bar"
  },
  {
    title: "Corporate Governance",
    description: "Support for corporations, not-for-profits, and partnerships with comprehensive secretarial services",
    icon: "building-columns"
  }
];
---

<BusinessLayout
  title="Auditing & Accounting Services | Jivish & Associates"
  description="Professional auditing and accounting solutions ensuring compliance, accuracy, and efficiency"
  theme={theme}
>
  <BusinessHeroAstro
    title="Auditing & Accounting Services"
    subtitle="Ensuring compliance, accuracy and efficiency for your business"
    backgroundImage="/images/image_2.jpeg"
    theme={theme}
  />

  <section class="business-section">
    <div class="business-container">
      <h2 class="business-heading">Our Auditing Services</h2>
      <p class="mb-6 text-lg">
        Jivish & Associates, based in Georgetown, Guyana, offers customized auditing services tailored to ensure compliance, accuracy, and efficiency for businesses across various industries.
      </p>
      <p class="mb-6 text-lg">
        Our mission is to deliver value through expert auditing services, supported by a skilled team and advanced technology.
      </p>
    </div>
  </section>

  <section class="business-section bg-gray-50">
    <div class="business-container">
      <h2 class="business-heading">Audit Services</h2>
      <p class="mb-6 text-lg">Delivering assurance and non-assurance support backed by 25+ years of expertise</p>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {auditServices.map((service) => (
          <ServiceCard
            title={service.title}
            description={service.description}
            icon={service.icon}
          />
        ))}
      </div>
    </div>
  </section>

  <section class="business-section">
    <div class="business-container">
      <h2 class="business-heading">Accounting Services</h2>
      <p class="mb-6 text-lg">End-to-end accounting solutions from system design to financial reporting</p>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {accountingServices.map((service) => (
          <ServiceCard
            title={service.title}
            description={service.description}
            icon={service.icon}
          />
        ))}
      </div>
    </div>
  </section>

  <section class="business-section bg-gray-50">
    <div class="business-container">
      <h2 class="business-heading">Additional Services</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {additionalServices.map((service) => (
          <ServiceCard
            title={service.title}
            description={service.description}
            icon={service.icon}
          />
        ))}
      </div>
    </div>
  </section>

  <section class="business-section">
    <div class="business-container">
      <h2 class="business-heading">Leadership Profiles</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        {leadershipProfiles.map((leader) => (
          <div class="p-6 bg-white rounded-lg shadow-md">
            <h3 class="text-2xl font-bold mb-2">{leader.name}</h3>
            <h4 class="text-xl font-semibold text-gray-600 mb-4">{leader.role}</h4>
            <p class="mb-2"><strong>Background:</strong> {leader.background}</p>
            <p class="mb-2"><strong>Qualifications/Experience:</strong> {leader.qualifications || leader.experience}</p>
            <p class="mb-2"><strong>Focus/Expertise:</strong> {leader.focus || leader.expertise}</p>
            <p><strong>Leadership:</strong> {leader.leadership}</p>
          </div>
        ))}
      </div>
    </div>
  </section>

  <section class="business-section bg-gray-50">
    <div class="business-container">
      <h2 class="business-heading">Why Choose Us</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {whyChooseUs.map((feature) => (
          <ServiceCard
            title={feature.title}
            description={feature.description}
            icon={feature.icon}
          />
        ))}
      </div>
    </div>
  </section>

  <section class="business-section">
    <div class="business-container">
      <h2 class="business-heading">Contact Us</h2>
      <div class="max-w-2xl mx-auto">
        <ContactForm serviceType="legal" theme={theme} />
        <div class="mt-6 text-center">
          <p>Lot 6 Church Street, Company Path, Cummingsburg, Georgetown, Guyana</p>
          <p class="mt-2">Phone: +************ | Email: <EMAIL></p>
          <p class="mt-2">Website: <a href="http://www.jivishgy.com" class="text-blue-600">www.jivishgy.com</a></p>
        </div>
      </div>
    </div>
  </section>
</BusinessLayout>
