---
import BusinessLayout from "../layouts/BusinessLayout.astro";
import BusinessHero from "../components/businesses/BusinessHero";
import ServiceCard from "../components/businesses/ServiceCard.astro";
import ContactForm from "../components/businesses/ContactForm";

const theme = {
  primary: "#1a1a1a",
  secondary: "#4a4a4a",
  accent: "#808080",
};

const auditServices = [
  {
    title: "Assurance Services",
    description: "Comprehensive audits to ensure financial accuracy and regulatory adherence",
    icon: "shield-check"
  },
  {
    title: "Non-Assurance Services",
    description: "Reviews and advisory to support business objectives",
    icon: "clipboard-list"
  },
  {
    title: "Quality Control",
    description: "Highest standards validated by ACCA reviews for ICAG",
    icon: "star"
  },
  {
    title: "Industry Expertise",
    description: "Serving manufacturing, trading, construction, medical, schools, extractive industries, and airlines",
    icon: "building"
  }
];

const accountingServices = [
  {
    title: "System Design & Implementation",
    description: "Custom accounting systems to streamline financial processes",
    icon: "settings"
  },
  {
    title: "Data Entry & Management",
    description: "Accurate recording of financial transactions",
    icon: "database"
  },
  {
    title: "Budgeting & MIS Reports",
    description: "Tools for effective financial planning and decision-making",
    icon: "trending-up"
  },
  {
    title: "IFRS Financial Reporting",
    description: "Compliant and transparent financial statements",
    icon: "file-text"
  },
  {
    title: "Financial Analysis",
    description: "Detailed insights including ageing reports",
    icon: "bar-chart"
  },
  {
    title: "Asset Accounting",
    description: "Fixed Assets Register, depreciation, and amortization",
    icon: "package"
  }
];

const whyChooseUs = [
  {
    title: "Experienced Leadership",
    description: "25+ years expertise from Vishwamint Ramnarine, Chartered Accountant",
    icon: "award"
  },
  {
    title: "Professional Team",
    description: "Energetic, trained professionals ensuring prompt service",
    icon: "users"
  },
  {
    title: "Technology-Driven",
    description: "Secure networked systems with backups and zero downtime",
    icon: "cpu"
  },
  {
    title: "Client-Centric Approach",
    description: "Long-term relationships through personalized solutions",
    icon: "heart"
  }
];
---

<BusinessLayout
  title="Auditing & Accounting Services | Jivish & Associates"
  description="Professional auditing and accounting solutions ensuring compliance, accuracy, and efficiency"
  theme={theme}
>
  <BusinessHero
    title="Auditing & Accounting Services"
    subtitle="Ensuring compliance, accuracy and efficiency for your business"
    backgroundImage="/images/auditing-hero.jpg"
    theme={theme}
  />

  <section class="business-section">
    <div class="business-container">
      <h2 class="business-heading">Audit Services</h2>
      <p class="mb-6 text-lg">Delivering assurance and non-assurance support backed by 25+ years of expertise</p>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {auditServices.map((service) => (
          <ServiceCard
            title={service.title}
            description={service.description}
            icon={service.icon}
          />
        ))}
      </div>
    </div>
  </section>

  <section class="business-section bg-gray-50">
    <div class="business-container">
      <h2 class="business-heading">Accounting Services</h2>
      <p class="mb-6 text-lg">End-to-end accounting solutions from system design to financial reporting</p>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {accountingServices.map((service) => (
          <ServiceCard
            title={service.title}
            description={service.description}
            icon={service.icon}
          />
        ))}
      </div>
    </div>
  </section>

  <section class="business-section">
    <div class="business-container">
      <h2 class="business-heading">Why Choose Us</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {whyChooseUs.map((feature) => (
          <ServiceCard
            title={feature.title}
            description={feature.description}
            icon={feature.icon}
          />
        ))}
      </div>
    </div>
  </section>

  <section class="business-section bg-gray-50">
    <div class="business-container">
      <h2 class="business-heading">Contact Us</h2>
      <div class="max-w-2xl mx-auto">
        <ContactForm serviceType="legal" theme={theme} />
        <div class="mt-6 text-center">
          <p>Lot 6 Church Street, Company Path, Cummingsburg, Georgetown, Guyana</p>
          <p class="mt-2">Phone: +************ | Email: <EMAIL></p>
          <p class="mt-2">Website: <a href="http://www.jivishgy.com" class="text-blue-600">www.jivishgy.com</a></p>
        </div>
      </div>
    </div>
  </section>
</BusinessLayout>
]]>
