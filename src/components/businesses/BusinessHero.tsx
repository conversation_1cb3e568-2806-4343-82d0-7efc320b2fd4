import React from 'react';

interface BusinessHeroProps {
    title: string;
    subtitle: string;
    backgroundImage: string;
    theme: {
        primary: string;
        secondary: string;
        accent: string;
    };
}

const BusinessHero: React.FC<BusinessHeroProps> = ({
    title,
    subtitle,
    backgroundImage,
    theme,
}) => {
    return (
        <div className="relative w-full h-[45vh] min-h-[400px] max-h-[45vh] overflow-hidden">
            <img
                src={backgroundImage}
                alt={title}
                className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-black bg-opacity-40 flex flex-col items-center justify-center text-center p-6">
                <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">{title}</h1>
                <p className="text-xl md:text-2xl text-white max-w-3xl">{subtitle}</p>
            </div>
        </div>
    );
};

export default BusinessHero; 
