---
interface Props {
    title: string;
    description: string;
    icon: string;
}

const { title, description, icon } = Astro.props;
---

<div class="bg-white p-6 rounded-lg shadow-md">
    <div
        class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4"
    >
        <i class={`fas fa-${icon} text-blue-600 text-xl`}></i>
    </div>
    <h3 class="text-xl font-semibold mb-2">{title}</h3>
    <p class="text-gray-600">{description}</p>
</div>
