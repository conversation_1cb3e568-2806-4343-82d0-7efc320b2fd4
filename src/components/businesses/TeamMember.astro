---
interface Props {
    name: string;
    role: string;
    specialization: string;
    image: string;
}

const { name, role, specialization, image } = Astro.props;
---

<div class="bg-white p-6 rounded-lg shadow-md">
    <img
        src={image}
        alt={name}
        class="w-full h-64 object-cover rounded-lg mb-4"
    />
    <h3 class="text-xl font-semibold mb-1">{name}</h3>
    <p class="text-gray-600 mb-1">{role}</p>
    <p class="text-sm text-gray-500">{specialization}</p>
</div>
