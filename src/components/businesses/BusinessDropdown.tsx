import React from 'react';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { ChevronDown } from "lucide-react";

interface BusinessDropdownProps {
    className?: string;
}

const BusinessDropdown: React.FC<BusinessDropdownProps> = ({ className }) => {
    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button variant="ghost" className={className}>
                    Businesses <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
                <DropdownMenuItem asChild>
                    <a href="/farming">Farming</a>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                    <a href="/real-estate">Real Estate</a>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                    <a href="/legal-services">Legal Services</a>
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    );
};

export default BusinessDropdown; 