import React, { useState, useEffect } from 'react';

interface Testimonial {
    name: string;
    role: string;
    content: string;
}

interface TestimonialSliderProps {
    testimonials: Testimonial[];
}

const TestimonialSlider: React.FC<TestimonialSliderProps> = ({ testimonials }) => {
    const [currentIndex, setCurrentIndex] = useState(0);

    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentIndex((prevIndex) =>
                prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
            );
        }, 5000);

        return () => clearInterval(timer);
    }, [testimonials.length]);

    const currentTestimonial = testimonials[currentIndex];

    return (
        <div className="relative">
            <div className="bg-white rounded-lg shadow-md p-8 text-center">
                <blockquote className="text-xl italic text-gray-700 mb-6">
                    "{currentTestimonial.content}"
                </blockquote>
                <div className="text-gray-600">
                    <p className="font-semibold">{currentTestimonial.name}</p>
                    <p className="text-sm">{currentTestimonial.role}</p>
                </div>
            </div>

            <div className="flex justify-center mt-4 space-x-2">
                {testimonials.map((_, index) => (
                    <button
                        key={index}
                        onClick={() => setCurrentIndex(index)}
                        className={`w-2 h-2 rounded-full transition-colors ${index === currentIndex ? 'bg-blue-600' : 'bg-gray-300'
                            }`}
                        aria-label={`Go to testimonial ${index + 1}`}
                    />
                ))}
            </div>
        </div>
    );
};

export default TestimonialSlider; 