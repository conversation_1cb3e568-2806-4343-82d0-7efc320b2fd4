---
import { Image } from 'astro:assets';

interface Props {
    title: string;
    subtitle: string;
    backgroundImage: string;
    theme: {
        primary: string;
        secondary: string;
        accent: string;
    };
}

const { title, subtitle, backgroundImage, theme } = Astro.props;
---

<div class="relative w-full h-[45vh] min-h-[400px] max-h-[45vh] overflow-hidden">
    <img
        src={backgroundImage}
        alt={title}
        class="w-full h-full object-cover"
    />
</div>