import React from 'react';

interface BusinessHeroCarouselProps {
  images: string[];
  title: string;
  subtitle: string;
  theme: {
    primary: string;
    secondary: string;
    accent: string;
  };
}

const BusinessHeroCarousel: React.FC<BusinessHeroCarouselProps> = ({
  images,
  title,
  subtitle,
  theme,
}) => {
  const [currentIndex, setCurrentIndex] = React.useState(0);

  // Debug current index changes
  React.useEffect(() => {
    console.log('Current index changed to:', currentIndex);
  }, [currentIndex]);

  // Auto-rotate carousel
  React.useEffect(() => {
    console.log('Setting up auto-rotation, images length:', images.length);
    if (images.length <= 1) return;

    const interval = setInterval(() => {
      console.log('Auto-rotation triggered');
      setCurrentIndex((prevIndex) => {
        const newIndex = prevIndex === images.length - 1 ? 0 : prevIndex + 1;
        console.log('Auto-rotation: changing from', prevIndex, 'to', newIndex);
        return newIndex;
      });
    }, 3000);

    return () => {
      console.log('Cleaning up auto-rotation interval');
      clearInterval(interval);
    };
  }, [images.length]);

  const goToPrevious = () => {
    console.log('Previous button clicked, current index:', currentIndex);
    setCurrentIndex(currentIndex === 0 ? images.length - 1 : currentIndex - 1);
  };

  const goToNext = () => {
    console.log('Next button clicked, current index:', currentIndex);
    setCurrentIndex(currentIndex === images.length - 1 ? 0 : currentIndex + 1);
  };

  if (!images || images.length === 0) {
    return (
      <div className="relative w-full h-[45vh] min-h-[400px] max-h-[45vh] overflow-hidden bg-gray-200 flex items-center justify-center">
        <p className="text-gray-500">No images available</p>
      </div>
    );
  }

  return (
    <div className="relative w-full h-[45vh] min-h-[400px] max-h-[45vh] overflow-hidden">
      {/* Image container */}
      <div className="relative w-full h-full">
        <img
          src={images[currentIndex]}
          alt={`${title} - Image ${currentIndex + 1}`}
          className="w-full h-full object-cover transition-opacity duration-500"
        />

        {/* Overlay with content */}
        <div className="absolute inset-0 bg-black bg-opacity-40 flex flex-col items-center justify-center text-center p-6">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">{title}</h1>
          <p className="text-xl md:text-2xl text-white max-w-3xl">{subtitle}</p>
        </div>
      </div>

      {/* Navigation buttons - only show if more than 1 image */}
      {images.length > 1 && (
        <>
          <button
            onClick={goToPrevious}
            className="absolute left-4 top-1/2 -translate-y-1/2 z-50 bg-white hover:bg-gray-100 rounded-full p-3 transition-colors duration-200 shadow-lg border border-gray-200"
            aria-label="Previous image"
            style={{ pointerEvents: 'auto' }}
          >
            <svg className="w-6 h-6 text-gray-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          <button
            onClick={goToNext}
            className="absolute right-4 top-1/2 -translate-y-1/2 z-50 bg-white hover:bg-gray-100 rounded-full p-3 transition-colors duration-200 shadow-lg border border-gray-200"
            aria-label="Next image"
            style={{ pointerEvents: 'auto' }}
          >
            <svg className="w-6 h-6 text-gray-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </>
      )}

      {/* Dots indicator - only show if more than 1 image */}
      {images.length > 1 && (
        <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2 z-50">
          {images.map((_, index) => (
            <button
              key={index}
              onClick={() => {
                console.log('Dot clicked, going to index:', index);
                setCurrentIndex(index);
              }}
              className={`w-4 h-4 rounded-full transition-colors duration-200 border-2 ${
                index === currentIndex
                  ? 'bg-white border-white'
                  : 'bg-white/30 border-white/50 hover:bg-white/50'
              }`}
              aria-label={`Go to image ${index + 1}`}
              style={{ pointerEvents: 'auto' }}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default BusinessHeroCarousel;
