import React from 'react';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import Autoplay from 'embla-carousel-autoplay';

interface BusinessHeroCarouselProps {
  images: string[];
  title: string;
  subtitle: string;
  theme: {
    primary: string;
    secondary: string;
    accent: string;
  };
}

const BusinessHeroCarousel: React.FC<BusinessHeroCarouselProps> = ({
  images,
  title,
  subtitle,
  theme,
}) => {
  const plugin = React.useRef(
    Autoplay({ delay: 3000, stopOnInteraction: false })
  );

  return (
    <div className="relative w-full h-[45vh] min-h-[400px] max-h-[45vh] overflow-hidden">
       <Carousel
         className="w-full h-full"
         plugins={[plugin.current]}
         opts={{
           loop: true,
         }}
       >
         <CarouselContent>
           {images.map((src, index) => (
             <CarouselItem key={index}>
               <div className="flex h-full items-center justify-center">
                 <img
                   src={src}
                   alt={`Carousel item ${index + 1}`}
                   className="w-full h-full object-cover"
                 />
               </div>
             </CarouselItem>
           ))}
         </CarouselContent>
         <CarouselPrevious className="left-4" />
         <CarouselNext className="right-4" />
       </Carousel>
    </div>
  );
};

export default BusinessHeroCarousel;
