import React from "react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import Autoplay from 'embla-carousel-autoplay';

interface BusinessHeroCarouselProps {
  images: string[];
  title: string;
  subtitle: string;
  theme: {
    primary: string;
    secondary: string;
    accent: string;
  };
}

const BusinessHeroCarousel: React.FC<BusinessHeroCarouselProps> = ({
  images,
  title,
  subtitle,
  theme,
}) => {
  const [api, setApi] = React.useState<any>();
  const autoplayPlugin = React.useRef(
    Autoplay({
      delay: 3000,
      stopOnInteraction: false,
      stopOnMouseEnter: false,
      stopOnFocusIn: false
    })
  );

  // Debug: Log the images array and API
  React.useEffect(() => {
    console.log('BusinessHeroCarousel images:', images);
    console.log('Images length:', images.length);
    console.log('Carousel API:', api);

    if (api) {
      console.log('Carousel can scroll next:', api.canScrollNext());
      console.log('Carousel can scroll prev:', api.canScrollPrev());
    }
  }, [images, api]);

  // Force autoplay to start
  React.useEffect(() => {
    if (api && images.length > 1) {
      const autoplay = autoplayPlugin.current;
      if (autoplay) {
        console.log('Starting autoplay...');
        autoplay.play();
      }
    }
  }, [api, images.length]);

  if (!images || images.length === 0) {
    return (
      <div className="relative w-full h-[45vh] min-h-[400px] max-h-[45vh] overflow-hidden bg-gray-200 flex items-center justify-center">
        <p className="text-gray-500">No images available</p>
      </div>
    );
  }

  return (
    <div className="relative w-full h-[45vh] min-h-[400px] max-h-[45vh] overflow-hidden">
      <Carousel
        className="w-full h-full relative"
        plugins={[autoplayPlugin.current]}
        setApi={setApi}
        opts={{
          loop: true,
          align: "start",
        }}
      >
        <CarouselContent className="h-full">
          {images.map((src, index) => (
            <CarouselItem key={index} className="h-full">
              <div className="relative h-full">
                <img
                  src={src}
                  alt={`Carousel item ${index + 1}`}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-black bg-opacity-40 flex flex-col items-center justify-center text-center p-6">
                  <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">{title}</h1>
                  <p className="text-xl md:text-2xl text-white max-w-3xl">{subtitle}</p>
                </div>
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious className="absolute left-4 top-1/2 -translate-y-1/2 z-20 bg-white/80 hover:bg-white" />
        <CarouselNext className="absolute right-4 top-1/2 -translate-y-1/2 z-20 bg-white/80 hover:bg-white" />
      </Carousel>
    </div>
  );
};

export default BusinessHeroCarousel;
