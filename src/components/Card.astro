---
interface Props {
  title: string;
  content: string;
}

const { title, content } = Astro.props;
---

<div class="rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 p-6 bg-white max-w-sm overflow-hidden">
  <img 
    src="https://picsum.photos/200" 
    alt="Card image"
    class="w-full h-48 object-cover mb-4 rounded-lg"
    width="200"
    height="200"
  />
  <h2 class="text-2xl font-bold mb-3 text-gray-800">{title}</h2>
  <p class="text-gray-600 mb-4">
    {content}
  </p>
  <div class="flex justify-end">
    <button class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
      Read More
    </button>
  </div>
</div>