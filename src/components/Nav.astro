---
import { BusinessDropdown } from "@/components/ui/BusinessDropdown";
---

<header class="bg-white shadow-sm">
  <div class="container mx-auto px-4 py-4">
    <nav class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <a href="/">
          <img src="/images/logo.jpg" alt="Javid Site Logo" class="h-20 w-auto" />
        </a>
        <BusinessDropdown client:load />
      </div>

      <!-- Desktop Navigation -->
      <div class="hidden md:flex items-center space-x-6">
        <a href="/" class="text-gray-700 hover:text-gray-900">Home</a>
        <a href="/about" class="text-gray-700 hover:text-gray-900">About</a>
        <a href="/contact" class="text-gray-700 hover:text-gray-900">Contact</a>
      </div>

      <!-- Mobile Menu Button -->
      <button
        id="mobile-menu-button"
        class="md:hidden p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100"
        aria-label="Toggle menu"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 6h16M4 12h16M4 18h16"></path>
        </svg>
      </button>
    </nav>

    <!-- Mobile Navigation -->
    <div id="mobile-menu" class="hidden md:hidden mt-4 py-2">
      <div class="flex flex-col space-y-2">
        <a href="/" class="text-gray-700 hover:text-gray-900 px-4 py-2">Home</a>
        <a href="/about" class="text-gray-700 hover:text-gray-900 px-4 py-2"
          >About</a
        >
        <div class="px-4 py-2">
          <BusinessDropdown client:load />
        </div>
        <a href="/contact" class="text-gray-700 hover:text-gray-900 px-4 py-2"
          >Contact</a
        >
      </div>
    </div>
  </div>
</header>

<script>
  const mobileMenuButton = document.getElementById("mobile-menu-button");
  const mobileMenu = document.getElementById("mobile-menu");

  if (mobileMenuButton && mobileMenu) {
    mobileMenuButton.addEventListener("click", () => {
      mobileMenu.classList.toggle("hidden");
    });

    // Close mobile menu when clicking outside
    document.addEventListener("click", (event) => {
      const isClickInside =
        mobileMenuButton.contains(event.target as Node) ||
        mobileMenu.contains(event.target as Node);

      if (!isClickInside && !mobileMenu.classList.contains("hidden")) {
        mobileMenu.classList.add("hidden");
      }
    });
  }
</script>
