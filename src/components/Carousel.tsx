import {
    Carousel,
    CarouselContent,
    CarouselItem,
    CarouselNext,
    CarouselPrevious,
  } from "@/components/ui/carousel"
  import Autoplay from 'embla-carousel-autoplay';
  
  export default function CarouselDemo() {
    return (
        <Carousel 
          className="w-full max-w-3xl" 
          plugins={[
            Autoplay({ delay: 2000 })
          ]}
          opts={{
            loop: true
          }}
        >
        <CarouselContent>
          <CarouselItem>
            <div className="flex h-[400px] items-center justify-center bg-amber-100">
              <img 
                src="https://picsum.photos/800/400?random=1" 
                alt="Carousel item 1"
                className="h-full w-full object-cover"
              />
            </div>
          </CarouselItem>
          <CarouselItem>
            <div className="flex h-[400px] items-center justify-center bg-blue-100">
              <img 
                src="https://picsum.photos/800/400?random=2" 
                alt="Carousel item 2"
                className="h-full w-full object-cover"
              />
            </div>
          </CarouselItem>
          <CarouselItem>
            <div className="flex h-[400px] items-center justify-center bg-green-100">
              <img 
                src="https://picsum.photos/800/400?random=3" 
                alt="Carousel item 3"
                className="h-full w-full object-cover"
              />
            </div>
          </CarouselItem>
        </CarouselContent>
        <CarouselPrevious className="left-4" />
        <CarouselNext className="right-4" />
      </Carousel>
    )
  }
