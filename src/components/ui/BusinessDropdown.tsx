import * as React from "react"
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@radix-ui/react-dropdown-menu"
import { ChevronDown } from "lucide-react"

const businesses = [
    { name: "Farming", href: "/businesses/farming" },
    { name: "Real Estate", href: "/businesses/real-estate" },
    { name: "Legal Services", href: "/businesses/legal-services" },
]

export function BusinessDropdown() {
    return (
        <DropdownMenu>
            <DropdownMenuTrigger className="flex items-center text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors">
                Businesses
                <ChevronDown className="ml-1 h-4 w-4" />
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56 bg-white rounded-md shadow-lg py-1 z-50">
                {businesses.map((business) => (
                    <DropdownMenuItem key={business.href}>
                        <a
                            href={business.href}
                            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-gray-900 w-full text-left"
                        >
                            {business.name}
                        </a>
                    </DropdownMenuItem>
                ))}
            </DropdownMenuContent>
        </DropdownMenu>
    )
} 