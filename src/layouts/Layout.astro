---
import "../styles/globals.css";
import Nav from "../components/Nav.astro";
import { ViewTransitions } from "astro:transitions";

interface Props {
	title: string;
	description?: string;
}

const {
	title: propTitle,
	description = "Multi-business website with Farming, Real Estate, and Legal Services",
} = Astro.props;
---

<!doctype html>
<html lang="en" transition:animate="fade">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<meta name="description" content={description} />
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<meta name="generator" content={Astro.generator} />
		<title>{propTitle}</title>
		<ViewTransitions />
	</head>
	<body class="min-h-screen flex flex-col bg-gray-50">
		<Nav />
		<main
			class="flex-grow container mx-auto px-4 py-8"
			transition:animate="fade"
		>
			<slot />
		</main>
		<footer class="bg-white shadow-inner py-6 mt-8">
			<div class="container mx-auto px-4 text-center text-gray-600">
				<p>© 2025 JIVISHGY. All rights reserved.</p>
			</div>
		</footer>
	</body>
</html>
