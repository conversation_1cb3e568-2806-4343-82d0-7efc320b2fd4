---
import Layout from "./Layout.astro";

interface Props {
  title: string;
  description: string;
  theme: {
    primary: string;
    secondary: string;
    accent: string;
  };
}

const { title, description, theme } = Astro.props;
---

<Layout title={title} description={description}>
  <main class="min-h-screen">
    <slot />
  </main>
</Layout>

<style
  define:vars={{
    primary: theme.primary,
    secondary: theme.secondary,
    accent: theme.accent,
  }}
>
  :global(.business-section) {
    @apply py-16 px-4;
  }

  :global(.business-container) {
    @apply max-w-6xl mx-auto;
  }

  :global(.business-heading) {
    @apply text-4xl font-bold mb-8;
    color: var(--primary);
  }

  :global(.business-subheading) {
    @apply text-2xl font-semibold mb-6;
    color: var(--secondary);
  }

  :global(.business-text) {
    @apply text-gray-700 mb-4;
  }

  :global(.business-button) {
    @apply px-6 py-2 rounded-md font-medium transition-colors;
    background-color: var(--accent);
    color: white;
  }

  :global(.business-button:hover) {
    opacity: 0.9;
  }
</style>

