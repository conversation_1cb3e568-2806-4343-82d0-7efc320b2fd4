#!/usr/bin/env node

/**
 * Website Testing Script
 * 
 * This script runs automated tests for the multi-business website,
 * including Lighthouse audits for performance, accessibility, and SEO.
 * 
 * Usage:
 *   node scripts/test-website.js [url]
 * 
 * If no URL is provided, it will default to http://localhost:3000
 */

const lighthouse = require('lighthouse');
const chromeLauncher = require('chrome-launcher');
const fs = require('fs').promises;
const path = require('path');
const broken = require('broken-link-checker');
const puppeteer = require('puppeteer');

// Configuration
const BASE_URL = process.env.TEST_URL || 'http://localhost:3000';
const PAGES_TO_TEST = [
    '/',
    '/about',
    '/contact',
    '/blog',
    '/farming',
    '/farming/equipment',
    '/farming/services',
    '/real-estate',
    '/real-estate/properties',
    '/real-estate/agents',
    '/legal',
    '/legal/services',
    '/legal/team'
];

// Lighthouse configuration
const lighthouseConfig = {
    extends: 'lighthouse:default',
    settings: {
        onlyCategories: ['performance', 'accessibility', 'best-practices', 'seo'],
        formFactor: 'desktop',
        throttling: {
            rttMs: 40,
            throughputKbps: 10240,
            cpuSlowdownMultiplier: 1,
        },
    },
};

async function runLighthouse(url) {
    const chrome = await chromeLauncher.launch({ chromeFlags: ['--headless'] });
    const options = {
        logLevel: 'info',
        output: 'json',
        port: chrome.port,
    };

    try {
        const results = await lighthouse(url, options, lighthouseConfig);
        await chrome.kill();
        return results.lhr;
    } catch (error) {
        console.error(`Error running Lighthouse for ${url}:`, error);
        await chrome.kill();
        return null;
    }
}

async function checkBrokenLinks(url) {
    return new Promise((resolve) => {
        const siteChecker = new broken.SiteChecker({
            excludeExternalLinks: false,
            filterLevel: 3,
            honorRobotExclusions: true,
            maxSocketsPerHost: 5,
            requestMethod: 'head',
            retryCount: 3,
            retryDelay: 1000,
            userAgent: 'Mozilla/5.0 (compatible; MultiBusinessTester/1.0;)',
        }, {
            link: (result) => {
                if (result.broken) {
                    console.error(`Broken link found: ${result.url.resolved}`);
                }
            },
            complete: () => {
                resolve();
            },
        });

        siteChecker.enqueue(new URL(url));
    });
}

async function checkResponsiveDesign(url) {
    const browser = await puppeteer.launch({ headless: 'new' });
    const page = await browser.newPage();

    const viewports = [
        { width: 1920, height: 1080, deviceScaleFactor: 1 },
        { width: 768, height: 1024, deviceScaleFactor: 2 },
        { width: 375, height: 667, deviceScaleFactor: 2 },
    ];

    const results = [];

    for (const viewport of viewports) {
        await page.setViewport(viewport);
        await page.goto(url, { waitUntil: 'networkidle0' });

        // Check for common responsive design issues
        const hasHorizontalScroll = await page.evaluate(() => {
            return document.documentElement.scrollWidth > document.documentElement.clientWidth;
        });

        const viewportMeta = await page.evaluate(() => {
            const meta = document.querySelector('meta[name="viewport"]');
            return meta ? meta.content : null;
        });

        results.push({
            viewport,
            hasHorizontalScroll,
            hasViewportMeta: !!viewportMeta,
        });
    }

    await browser.close();
    return results;
}

async function saveResults(pagePath, results) {
    const resultsDir = path.join(process.cwd(), 'test-results');
    await fs.mkdir(resultsDir, { recursive: true });

    const filename = pagePath.replace(/\//g, '-').replace(/^-/, '') || 'home';
    const filePath = path.join(resultsDir, `${filename}.json`);

    await fs.writeFile(filePath, JSON.stringify(results, null, 2));
    console.log(`Results saved to ${filePath}`);
}

async function generateSummary() {
    const resultsDir = path.join(process.cwd(), 'test-results');
    const files = await fs.readdir(resultsDir);

    const summary = {
        timestamp: new Date().toISOString(),
        pages: {},
        averages: {
            performance: 0,
            accessibility: 0,
            bestPractices: 0,
            seo: 0,
        },
    };

    let count = 0;

    for (const file of files) {
        if (file === 'test-summary.json') continue;

        const content = await fs.readFile(path.join(resultsDir, file), 'utf-8');
        const results = JSON.parse(content);

        if (results.lighthouse) {
            const categories = results.lighthouse.categories;
            summary.pages[file.replace('.json', '')] = {
                performance: categories.performance.score * 100,
                accessibility: categories.accessibility.score * 100,
                bestPractices: categories['best-practices'].score * 100,
                seo: categories.seo.score * 100,
            };

            summary.averages.performance += categories.performance.score * 100;
            summary.averages.accessibility += categories.accessibility.score * 100;
            summary.averages.bestPractices += categories['best-practices'].score * 100;
            summary.averages.seo += categories.seo.score * 100;
            count++;
        }
    }

    if (count > 0) {
        summary.averages.performance /= count;
        summary.averages.accessibility /= count;
        summary.averages.bestPractices /= count;
        summary.averages.seo /= count;
    }

    await fs.writeFile(
        path.join(resultsDir, 'test-summary.json'),
        JSON.stringify(summary, null, 2)
    );

    console.log('Summary report generated');
}

async function main() {
    console.log(`Starting tests for ${BASE_URL}`);

    for (const pagePath of PAGES_TO_TEST) {
        const url = `${BASE_URL}${pagePath}`;
        console.log(`\nTesting ${url}`);

        const results = {
            url,
            timestamp: new Date().toISOString(),
            lighthouse: null,
            brokenLinks: [],
            responsiveDesign: null,
        };

        // Run Lighthouse
        console.log('Running Lighthouse audit...');
        results.lighthouse = await runLighthouse(url);

        // Check for broken links
        console.log('Checking for broken links...');
        await checkBrokenLinks(url);

        // Check responsive design
        console.log('Checking responsive design...');
        results.responsiveDesign = await checkResponsiveDesign(url);

        // Save results
        await saveResults(pagePath, results);
    }

    // Generate summary
    await generateSummary();
    console.log('\nTesting completed!');
}

main().catch(console.error); 